<script setup lang="ts">
import FormTransactionType from '@/components/form/transaction_type/FormTransactionType.vue';
import Heading from '@/components/Heading.vue';
import FaIcon from '@/components/FaIcon.vue';
import TabsWrapper from '@/components/TabsWrapper.vue';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, router, useForm } from '@inertiajs/vue3';
import { computed, ref, onMounted } from 'vue';
import { Button } from '@/components/ui/button';
import {
    LoanInstallment,
    LoanInstallmentBalance,
    LoanInstallmentCurrent,
    LoanTransaction,
    LoanPaymentRecord,
    LoanTransactionBalance,
    Selection,
} from '@/types';
import type { PaginatedData } from '@/types/table';
import Pagination from '@/components/Pagination.vue';
import ShowingEntries from '@/components/ShowingEntries.vue';
import DataTable from '@/components/datatable/DataTable.vue';
import { formatDateTime } from '@/utils/dateUtils';
import { useFormSubmit } from '@/composables/useFormSubmit';

const { submitWithConfirmation } = useFormSubmit();

interface Props {
    installments: PaginatedData<LoanInstallment>;
    transactions: PaginatedData<LoanTransaction>;
    paymentRecords: PaginatedData<LoanPaymentRecord>;
    loan: any;
    current: LoanInstallmentCurrent;
    balanceInstallment: LoanInstallmentBalance;
    balanceTransaction: LoanTransactionBalance;
    transactionTypes: Selection[];
    letterTypes: Selection[];
    paymentMethods: Selection[];
    from: string;
}

const props = defineProps<Props>();

const activeTab = ref('general');
const isLoading = ref(false);

const tabItems = computed(() => [
    { label: 'General', value: 'general' },
    { label: 'Transaction', value: 'transaction' },
    { label: 'Transaction Records', value: 'transaction-record' },
]);

const statusLabel = (status: number) => {
    const labels = [
        'Draft', // 0
        'Pending Process', // 1
        'Pending Review', // 2
        'Pending Approval', // 3
        'Rejected', // 4
        'Approved', // 5
        'Customer Rejected', // 6
        'Customer Accepted', // 7
        'On-going', // 8
        'On-going (Overdue)', // 9
        'Completed', // 10
        'Cancelled', // 11
    ];
    return labels[status] ?? 'Unknown';
};

const form = useForm({
    selection_transaction_type_id: null as number | null,
    no_instalment: null as number | null,
    selection_letter_type_id: null as number | null,
    ar_serial_no: [''] as string[],
    amount: null as string | null,
    payment_date: null as string | null,
    selection_payment_method_id: null as number | null,
    payment_ref_no: null as string | null,
    rebate_amount: null as string | null,
    rebate_tenure: null as number | null,
    description: null as string | null,
});

const goToLoanDetailTab = () => {
    router.visit(route(`loans.${props.from}`, props.loan.id));
};

const columns = [
    { field: 'tenure', label: 'No.', width: 'w-8' },
    { field: 'pay_date', label: 'Date', width: 'w-24' },
    { field: 'due_date', label: 'Due Date', width: 'w-24' },
    {
        field: 'tenure',
        label: 'Tenure',
        width: 'w-24',
        format: (value: string) => `${value}/${props.installments.total}`,
    },
    { field: 'principle_amount', label: 'Principal Amount (RM)', width: 'w-32', align: 'text-right' },
    { field: 'interest_amount_net', label: 'Interest Amount (RM)', width: 'w-32', align: 'text-right' },
    { field: 'late_charge_amount', label: 'Late Charges (RM)', width: 'w-32', align: 'text-right' },
    { field: 'postage_amount', label: 'Postage Charges (RM)', width: 'w-32', align: 'text-right' },
    { field: 'outstanding_balance_amount', label: 'Outstanding Balance (RM)', width: 'w-32', align: 'text-right' },
];

const transactionColumns = [
    { field: 'current_no', label: 'No.', width: 'w-16' },
    { field: 'txn_date', label: 'Date', width: 'w-16' },
    {
        field: 'tenure',
        label: 'Tenure',
        width: 'w-24',
        format: (value: string | number) => (Number(value) === 0 ? '-' : `${value}/${props.installments.total}`),
    },
    { field: 'txn_type', label: 'Type', width: 'w-32' },
    { field: 'code', label: 'Reference No.', width: 'w-32' },
    { field: 'amount', label: 'Debit (RM)', width: 'w-32', align: 'text-right' },
    { field: 'payment_amount', label: 'Payment (RM)', width: 'w-32', align: 'text-right' },
    { field: 'balance_amount', label: 'Balance (RM)', width: 'w-32', align: 'text-right' },
    { field: 'remark', label: 'Description', width: 'w-24' },
];

const paymentRecordColumns = [
    { field: 'code', label: 'Ref. No', width: 'w-8' },
    { field: 'txn_type', label: 'Type', width: 'w-24' },
    { field: 'payment_method', label: 'Payment Method', width: 'w-24' },
    { field: 'payment_ref_code', label: 'Payment Ref. No', width: 'w-24' },
    { field: 'amount', label: 'Repayment Amount (RM)', width: 'w-24', align: 'text-right' },
    {
        field: 'created_at',
        label: 'Created At',
        sortable: true,
        width: 'w-40',
        format: (value) => formatDateTime(value),
    },
    { field: 'created_by.name', label: 'Created By', sortable: true },
    { field: 'remark', label: 'Description', width: 'w-24' },
    { field: 'actions', label: 'Action', sortable: false, sticky: true, width: 'w-[50px]', align: 'text-center' },
];

const paymentDialogColumns = [
    { field: 'code', label: 'Ref No', width: 'w-8' },
    { field: 'payment_type', label: 'Payment Type', width: 'w-24' },
    { field: 'txn_type', label: 'Transaction Type', width: 'w-24' },
    { field: 'amount', label: 'Amount (RM)', width: 'w-24', align: 'text-right' },
    { field: 'remark', label: 'Description', width: 'w-24' },
];

const handlePaginate = (url: string) => {
    isLoading.value = true;
    router.get(
        url,
        {},
        {
            only: ['installments'],
            preserveState: true,
            preserveScroll: true,
            onFinish: () => {
                isLoading.value = false;
            },
        },
    );
};
const handlePaginate2 = (url: string) => {
    isLoading.value = true;
    router.get(
        url,
        {},
        {
            only: ['installments', 'transactions'],
            preserveState: true,
            preserveScroll: true,
            onFinish: () => {
                isLoading.value = false;
            },
        },
    );
};

const handlePaginate3 = (url: string) => {
    isLoading.value = true;
    router.get(
        url,
        {},
        {
            only: ['paymentRecords'],
            preserveState: true,
            preserveScroll: true,
            onFinish: () => {
                isLoading.value = false;
            },
        },
    );
};

const handleReceipt = ({ action, row }: { action: string; row: any }) => {
    const printUrl = route('payment-records.print', row.id);
    window.open(printUrl, '_blank');
};

const showModal = ref(false);
const selectedRecord = ref(null);

const handleView = (row) => {
    selectedRecord.value = row;
    showModal.value = true;
};

const submitTransaction = () => {
    const payload = {
        ...form,
        ar_serial_no: form.ar_serial_no.filter((v) => v.trim() !== '').join(','),
    };

    submitWithConfirmation({
        form: payload,
        submitOptions: {
            method: 'post',
            url: route('loan.transaction.post', { loan: props.loan.id }),
            entityName: 'transaction',
            resetForm: true,
        },
    });
};
</script>

<template>
    <AppLayout>
        <Head title="Loan" />
        <div class="px-4 py-3">
            <Heading title="Loans" pageNumber="P000012" description="Edit the selected loan record">
                <template #status>
                    <Badge
                        :class="[
                            {
                                'bg-ocean': props.loan.status === 1,
                                'bg-canary': props.loan.status === 2,
                                'bg-orange': props.loan.status === 3,
                                'bg-chrome': props.loan.status === 4,
                                'bg-castleton': props.loan.status === 5,
                                'bg-pink': props.loan.status === 6,
                                'bg-soften': props.loan.status === 7,
                                'bg-cobalt': props.loan.status === 8,
                                'bg-tomato': props.loan.status === 9,
                                'bg-green': props.loan.status === 10,
                                'bg-mist': props.loan.status === 11,
                            },
                            'text-md px-1 py-0',
                        ]"
                    >
                        {{ statusLabel(props.loan.status) }}
                    </Badge>
                </template>
                <template #slot>
                    <div class="border-gainsboro border">
                        <Button
                            type="button"
                            @click="goToLoanDetailTab"
                            class="bg-background hover:bg-background px-4 py-2 text-black hover:text-black"
                        >
                            <FaIcon name="sack-dollar" class="pr-3" />
                            Loan Details
                        </Button>
                        <Button type="button" @click="" class="bg-azure hover:bg-azure px-4 py-2 text-white hover:text-white">
                            <FaIcon name="right-left" class="pr-3" />
                            Transaction
                        </Button>
                    </div>
                </template>
            </Heading>
            <Card class="gap-0 py-0">
                <CardHeader class="bg-azure gap-0 rounded-t-lg px-5.5 py-3 text-white">
                    <CardTitle>Loan No.: {{ props.loan.code }}</CardTitle>
                </CardHeader>
                <TabsWrapper v-model="activeTab" :tabs="tabItems">
                    <template #general>
                        <CardContent class="px-6 py-2">
                            <Label class="text-[20px]" for="">General</Label>
                            <Label class="py-4 text-[20px]" for="">Loan Details</Label>
                            <div class="grid grid-cols-1 gap-3 lg:grid-cols-1">
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Loan Release Date</Label>
                                        <p>{{ props.loan.loanDetail.commencement_date }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Loan Type</Label>
                                        <p>{{ props.loan.type }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Loan Mode</Label>
                                        <p>{{ props.loan.loanDetail.mode_type }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Instalment Amount (RM)</Label>
                                        <p>{{ props.loan.loanDetail.instalment_amount }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Last Payment Amount (RM)</Label>
                                        <p>{{ props.loan.loanDetail.last_payment }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">No of Instalment (Tenure)</Label>
                                        <p>{{ props.loan.loanDetail.no_of_instalment }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Instalment Arrears (RM)</Label>
                                        <p>{{ props.balanceInstallment.total_arrears_amount }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Current Due Date</Label>
                                        <p>{{ props.current.due_date }}</p>
                                    </div>
                                </div>
                            </div>
                            <Label class="py-4 text-[20px]" for="">Loan Balance Status</Label>
                            <div class="grid grid-cols-1 gap-3 lg:grid-cols-1">
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Loan Principle Amount (RM)</Label>
                                        <p>{{ props.balanceInstallment.total_loan_principle_amount }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Interest Charges (RM) (+)</Label>
                                        <p>{{ props.balanceInstallment.total_interest_charges_amount }}</p>
                                    </div>
                                    <Separator class="my-4" />
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base">Balance Payable (RM)</Label>
                                        <p>{{ props.balanceInstallment.total_loan_amount }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Total Instalment Paid (RM) (-)</Label>
                                        <p>{{ props.balanceInstallment.total_payment_amount }}</p>
                                    </div>
                                    <Separator class="my-4" />
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base">Instalment Balance (RM)</Label>
                                        <p>{{ props.balanceInstallment.total_balance_amount }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Late Interest Charges (RM) (+)</Label>
                                        <p>{{ props.balanceInstallment.late_interest_charges_amount }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Postage Charges (RM) (+)</Label>
                                        <p>{{ props.balanceInstallment.postage_charges_amount }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Legal Fee (RM) (+)</Label>
                                        <p>{{ props.balanceInstallment.legal_charges_amount }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Misc Charges (RM) (+)</Label>
                                        <p>{{ props.balanceInstallment.misc_charges_amount }}</p>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base font-normal">Rebate (RM) (-)</Label>
                                        <p>{{ props.balanceInstallment.rebate_amount }}</p>
                                    </div>
                                    <Separator class="my-4" />
                                    <div class="flex items-center justify-between">
                                        <Label for="company-name" class="text-base">Current Balance (RM)</Label>
                                        <p>{{ props.balanceInstallment.total_outstanding_amount }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="relative mt-4 mb-4">
                                <!-- Loading overlay -->
                                <div v-if="isLoading" class="bg-opacity-75 absolute inset-0 z-10 flex items-center justify-center bg-white">
                                    <div class="flex items-center space-x-2">
                                        <div class="h-6 w-6 animate-spin rounded-full border-b-2 border-blue-600"></div>
                                        <span class="text-sm text-gray-600">Loading...</span>
                                    </div>
                                </div>
                                <DataTable
                                    :columns="columns"
                                    :data="installments.data"
                                    empty-message="No installments found."
                                    :showDeleteButton="false"
                                    :showStatusToggle="false"
                                    :showEditButton="false"
                                    :showViewButton="false"
                                    :sortState="{ field: 'tenure', direction: 'asc' }"
                                >
                                    <template #status_label="{ item }">
                                        <Badge
                                            :class="[
                                                {
                                                    'bg-green text-white': item.status === 1,
                                                    'bg-red text-white': item.status === 0 && item.is_overdue,
                                                    'bg-yellow text-black': item.status === 0 && !item.is_overdue,
                                                    'bg-orange text-white': item.status === 3,
                                                },
                                                'rounded px-2 py-1 text-xs',
                                            ]"
                                        >
                                            {{ item.status_label }}
                                            <span v-if="item.is_overdue" class="ml-1">({{ item.days_overdue }} days)</span>
                                        </Badge>
                                    </template>
                                </DataTable>
                            </div>
                            <div class="bg-white">
                                <div class="flex items-center justify-between">
                                    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                                        <div>
                                            <ShowingEntries
                                                :from="installments.from"
                                                :to="installments.to"
                                                :total="installments.total"
                                                entityName="installments"
                                            />
                                        </div>
                                        <Pagination :links="installments.links" @navigate="handlePaginate" />
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                        <!-- Installment data table implemented above -->
                    </template>
                    <template #transaction>
                        <FormTransactionType
                            :form="form"
                            :loan="props.loan"
                            :balance="props.balanceTransaction"
                            :current="props.current"
                            :from="props.from"
                            :transactionTypes="props.transactionTypes"
                            :paymentMethods="props.paymentMethods"
                            :letterTypes="props.letterTypes"
                            :submitTransactionClick="submitTransaction"
                        />
                        <CardContent class="px-6 py-2">
                            <div class="relative mt-0 mb-4">
                                <!-- Loading overlay -->
                                <div v-if="isLoading" class="bg-opacity-75 absolute inset-0 z-10 flex items-center justify-center bg-white">
                                    <div class="flex items-center space-x-2">
                                        <div class="h-6 w-6 animate-spin rounded-full border-b-2 border-blue-600"></div>
                                        <span class="text-sm text-gray-600">Loading...</span>
                                    </div>
                                </div>
                                <DataTable
                                    :columns="transactionColumns"
                                    :data="transactions.data"
                                    empty-message="No transactions found."
                                    :showDeleteButton="false"
                                    :showStatusToggle="false"
                                    :showEditButton="false"
                                    :showViewButton="false"
                                    :sortState="{ field: 'tenure', direction: 'asc' }"
                                >
                                    <template #status_label="{ item }">
                                        <Badge
                                            :class="[
                                                {
                                                    'bg-green text-white': item.status === 1,
                                                    'bg-red text-white': item.status === 0 && item.is_overdue,
                                                    'bg-yellow text-black': item.status === 0 && !item.is_overdue,
                                                    'bg-orange text-white': item.status === 3,
                                                },
                                                'rounded px-2 py-1 text-xs',
                                            ]"
                                        >
                                            {{ item.status_label }}
                                            <span v-if="item.is_overdue" class="ml-1">({{ item.days_overdue }} days)</span>
                                        </Badge>
                                    </template>
                                </DataTable>
                            </div>
                            <div class="bg-white">
                                <div class="flex items-center justify-between">
                                    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                                        <div>
                                            <ShowingEntries
                                                :from="transactions.from"
                                                :to="transactions.to"
                                                :total="transactions.total"
                                                entityName="transactions"
                                            />
                                        </div>
                                        <Pagination :links="transactions.links" @navigate="handlePaginate2" />
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </template>
                    <template #transaction-record>
                        <CardContent class="px-6 py-2">
                            <Label class="text-[20px]" for="">Transaction Records</Label>
                            <div class="relative mt-4 mb-4">
                                <!-- Loading overlay -->
                                <div v-if="isLoading" class="bg-opacity-75 absolute inset-0 z-10 flex items-center justify-center bg-white">
                                    <div class="flex items-center space-x-2">
                                        <div class="h-6 w-6 animate-spin rounded-full border-b-2 border-blue-600"></div>
                                        <span class="text-sm text-gray-600">Loading...</span>
                                    </div>
                                </div>
                                <DataTable
                                    :columns="paymentRecordColumns"
                                    :data="paymentRecords.data"
                                    empty-message="No transaction history found."
                                    :showDeleteButton="false"
                                    :showStatusToggle="false"
                                    :showEditButton="false"
                                    @view="handleView"
                                    :sortState="{ field: 'payment_date', direction: 'asc' }"
                                    :actionButtons="[
                                        {
                                            icon: 'receipt',
                                            color: 'text-teal',
                                            action: 'print',
                                            tooltip: 'View Receipt',
                                        },
                                    ]"
                                    @action="handleReceipt"
                                >
                                    <template #status_label="{ item }">
                                        <Badge
                                            :class="[
                                                {
                                                    'bg-green text-white': item.status === 1,
                                                    'bg-red text-white': item.status === 0 && item.is_overdue,
                                                    'bg-yellow text-black': item.status === 0 && !item.is_overdue,
                                                    'bg-orange text-white': item.status === 3,
                                                },
                                                'rounded px-2 py-1 text-xs',
                                            ]"
                                        >
                                            {{ item.status_label }}
                                            <span v-if="item.is_overdue" class="ml-1">({{ item.days_overdue }} days)</span>
                                        </Badge>
                                    </template>
                                </DataTable>
                            </div>
                            <div class="bg-white">
                                <div class="flex items-center justify-between">
                                    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                                        <div>
                                            <ShowingEntries
                                                :from="paymentRecords.from"
                                                :to="paymentRecords.to"
                                                :total="paymentRecords.total"
                                                entityName="paymentRecords"
                                            />
                                        </div>
                                        <Pagination :links="paymentRecords.links" @navigate="handlePaginate3" />
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </template>
                </TabsWrapper>
            </Card>
        </div>
        <Dialog v-model:open="showModal">
            <DialogContent class="flex max-h-[90vh] w-full max-w-4xl min-w-4xl flex-col rounded-xl bg-white px-6 py-4 shadow-xl">
                <DialogTitle class="mb-4 text-xl font-semibold">Transaction Detail</DialogTitle>
                <div class="min-h-0 flex-1 overflow-y-auto">
                    <DataTable
                        v-if="selectedRecord"
                        :columns="paymentDialogColumns"
                        :data="selectedRecord.payment_detail"
                        :sortState="{ field: 'code', direction: 'asc' }"
                        class="p-2"
                    >
                        <template #footer>
                            <tr class="bg-gray-100 font-semibold">
                                <td colspan="3" class="pr-4 text-right"></td>
                                <td class="pr-4 text-right">{{ selectedRecord.amount }}</td>
                                <td></td>
                            </tr>
                        </template>
                    </DataTable>
                </div>
            </DialogContent>
        </Dialog>
    </AppLayout>
</template>
