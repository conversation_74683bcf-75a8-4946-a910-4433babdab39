<?php

namespace App\Http\Resources\Loans;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * LoanPaymentResource
 *
 * Transforms LoanPayment model data for API responses.
 *
 * Usage examples:
 * - Single payment: new LoanPaymentResource($payment)
 * - Collection: LoanPaymentResource::collection($payments)
 */
class LoanReceiptResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        $company = $this->loan->company()->first();
        $team = $this->loan->team()->first();
        $teamContact = $team->telephone();
        $teamAddress = $team->address;
        $agent = $this->loan->agent()->first();

        $teamDetail = [
            'email' => $team->email,
            'website' => $team->website,
            'contact' => isset($teamContact, $teamContact->contactCountrySelection)
                ? $teamContact->contactCountrySelection->value.'-'.$teamContact->contact
                : null,
            'address' => $teamAddress ?? null,
        ];

        $companyDetail = [
            'company' => $this->loan->company,
            'company_logo' => $company->getLogoUrl()['url'] ?? null,
            'business_registration_no' => $company->business_registration_no ?? null,
            'old_business_registration_no' => $company->old_business_registration_no ?? null,
        ];

        $customerData = [];
        foreach ($this->loan->loanCustomerProfiles as $profile) {
            $customerData[] = [
                'name' => $profile->name,
                'identity_no' => $profile->identity_no,
            ];
            break;
        }

        $paymentTxnDetail = [];
        foreach ($this->paymentDetails as $paymentDetail) {
            $paymentTxnDetail[] = [
                'loan_txn_id' => $paymentDetail->loan_payment_id,
                'code' => $paymentDetail->loanTxn->code,
                'txn_type' => $paymentDetail->loanTxn->txn_type,
                'payment_type' => $this->payment_method,
                'amount' => $paymentDetail->amount,
                'remark' => $paymentDetail->loanTxn->remark,
            ];
        }

        $baseData = [
            'id' => $this->id,
            'company_detail' => $companyDetail,
            'team_detail' => $teamDetail,
            'agent' => $agent->display_name,
            'agent_code' => $agent->code,
            'customer' => $customerData,
            'code' => $this->code,
            'loan_no' => $this->loan->code,
            'loan_id' => $this->loan->uuid,
            'txn_date' => $this->txn_date,
            'txn_type' => $this->txn_type,
            'payment_detail' => $paymentTxnDetail,
            'payment_ref_code' => $this->payment_ref_code,
            'payment_method' => $this->payment_method,
            'repayment_date' => $this->payment_date,
            'amount' => number_format($this->amount ?? 0, 2),
            'rebate_amount' => number_format($this->rebate_amount ?? 0, 2),
            'remark' => $this->remark,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => $this->whenLoaded('createdBy', fn () => [
                'id' => $this->createdBy->id,
                'name' => $this->createdBy->username,
            ]),
            'updated_by' => $this->whenLoaded('updatedBy', fn () => [
                'id' => $this->updatedBy->id,
                'name' => $this->updatedBy->username,
            ]),
        ];

        return $baseData;
    }
}
