<?php

namespace App\Http\Controllers\Customers;

use App\Enums\Customer\CustomerStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Customers\SearchCustomerRequest;
use App\Http\Requests\Customers\StoreCustomerRequest;
use App\Http\Requests\Customers\UpdateCustomerRequest;
use App\Http\Resources\Customers\CustomerResource;
use App\Models\Address;
use App\Models\Collateral;
use App\Models\CollateralProperty;
use App\Models\CollateralPropertyOwner;
use App\Models\CollateralValuer;
use App\Models\Company;
use App\Models\Contact;
use App\Models\CustomerEmployment;
use App\Models\CustomerProfile;
use App\Models\Document;
use App\Models\Headquarter;
use App\Models\Owner;
use App\Models\Selection;
use App\Models\Team;
use App\Traits\HandlesFileStorage;
use App\Traits\QueryFilterableTrait;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

class CustomerController extends Controller
{
    use HandlesFileStorage, QueryFilterableTrait;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $this->authorize('viewAny', CustomerProfile::class);

        $query = CustomerProfile::forUser()
            ->withAuditUsers()
            ->with(['customerContacts.contacts', 'teams', 'selectionNationality'])
            ->when($request->filled('contact_no'), function ($query) use ($request) {
                return $query->whereHas('customerContacts', function ($q) use ($request) {
                    $q->whereHas('contacts', function ($q) use ($request) {
                        $q->where('contact', 'like', "%{$request->contact_no}%");
                    });
                });
            })
            ->when($request->filled('from_updated_at'), function ($query) use ($request) {
                $query->whereDate('updated_at', '>=', $request->from_updated_at);
            })
            ->when($request->filled('to_updated_at'), function ($query) use ($request) {
                $query->whereDate('updated_at', '<=', $request->to_updated_at);
            });

        $this->applySearchFilter($query, $request, 'display_name', 'name');
        $this->applySearchFilter($query, $request, 'identity_no');
        $this->applyStatusFilter($query, $request);
        $this->applySorting($query, $request);

        $customers = $this->applyPagination($query, $request, 10,
            fn ($customer) => (new CustomerResource($customer))->toArray($request));

        return Inertia::render('customers/Index', [
            'customers' => $customers,
            'statuses' => CustomerStatus::options(),
            'filters' => request()->only(['name', 'identity_no', 'contact_no', 'status', 'from_updated_at', 'to_updated_at', 'per_page', 'sort_field', 'sort_direction']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        $this->authorize('create', CustomerProfile::class);

        return Inertia::render('customers/Create', [
            'headquarters' => Headquarter::getForDropdown(),
            'companies' => Company::getForDropdown(),
            'teams' => Team::getForDropdown(),
            'customerTypes' => Selection::where('category', 'customer_type')->get(),
            'collateralTypes' => Selection::where('category', 'collateral_type')->get(),
            'propertyTypes' => Selection::where('category', 'property_type')->get(),
            'landCategories' => Selection::where('category', 'land_category')->get(),
            'landStatuses' => Selection::where('category', 'land_status')->get(),
            'states' => Selection::where('category', 'state')->get(),
            'countries' => Selection::where('category', 'country')->get(),
            'squareTypes' => Selection::where('category', 'square_unit')->get(),
            'telephoneTypes' => Selection::where('category', 'telephone_country')->get(),
            'mobileTypes' => Selection::where('category', 'mobile_country')->get(),
            'contactTypes' => Selection::where('category', 'contact_type')->get(),
            'ownerTypes' => Selection::where('category', 'owner_type')->get(),
            'addressTypes' => Selection::where('category', 'address_type')->get(),
            'businessClassificationTypes' => Selection::where('category', 'business_classification')->get(),
            'termsOfEmploymentTypes' => Selection::where('category', 'terms_of_employment')->get(),
            'occupationsTypes' => Selection::where('category', 'occupation')->get(),
            'nationalities' => Selection::where('category', 'nationality')->get(),
            'genders' => Selection::where('category', 'gender')->get(),
            'educationTypes' => Selection::where('category', 'education_level')->get(),
            'naturesOfBusinessTypes' => Selection::where('category', 'nature_of_business')->get(),
            'maritalStatus' => Selection::where('category', 'marital_status')->get(),
            'races' => Selection::where('category', 'race')->get(),
            'documentTypes' => Selection::where('category', 'document_type')->get(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreCustomerRequest $request): RedirectResponse
    {
        $this->authorize('create', CustomerProfile::class);

        DB::beginTransaction();
        try {
            $team = Team::find($request->team_id);

            $customer = CustomerProfile::create([
                'code' => Str::random(10),
                'headquarter_id' => $team->headquarter_id,
                'company_id' => $team->company_id,
                ...$request->except(['headquarter_id', 'company_id', 'team_id', 'contacts', 'addresses', 'employment', 'company', 'owners', 'collateral', 'document']),
            ]);

            $customer->teams()->attach($request->team_id);

            if ($request->has('contacts')) {
                foreach ($request->contacts as $contactData) {
                    $customerContact = $customer->customerContacts()->create();
                    if (isset($contactData['telephone'])) {
                        $customerContact->contacts()->create([
                            'selection_type_id' => $contactData['selection_type_id'],
                            'category' => Contact::CATEGORY_TELEPHONE,
                            'selection_country_id' => $contactData['selection_telephone_country_id'],
                            'contact' => $contactData['telephone'],
                            'is_primary' => $contactData['is_primary'],
                        ]);
                    }

                    if (isset($contactData['mobile_phone'])) {
                        $customerContact->contacts()->create([
                            'selection_type_id' => $contactData['selection_type_id'],
                            'category' => Contact::CATEGORY_MOBILE,
                            'selection_country_id' => $contactData['selection_mobile_country_id'],
                            'contact' => $contactData['mobile_phone'],
                            'can_receive_sms' => $contactData['can_receive_sms'],
                            'is_primary' => $contactData['is_primary'],
                        ]);
                    }
                }
            }

            if ($request->has('addresses')) {
                foreach ($request->addresses as $addressData) {
                    $customerAddress = $customer->customerAddresses()->create();
                    $address = $customerAddress->addresses()->create([
                        ...$addressData,
                    ]);
                }
            }

            if ($request->selection_type_id == 28) {
                if ($request->has('employment')) {
                    $employmentData = $request->employment;

                    // Extract nested data
                    $addressData = $employmentData['address'] ?? null;
                    $telephone = $employmentData['telephone'] ?? null;
                    $mobilePhone = $employmentData['mobile_phone'] ?? null;

                    // Remove nested fields from main employment data
                    unset(
                        $employmentData['address'],
                        $employmentData['telephone'],
                        $employmentData['mobile_phone'],
                    );

                    $employment = $customer->employment()->create($employmentData);

                    // Create address if available
                    if ($addressData) {
                        $employment->address()->create([
                            ...$addressData,
                            'is_primary' => true,
                        ]);
                    }
                    // Create telephone contact if available
                    if ($telephone) {
                        $employment->contacts()->create([
                            'category' => Contact::CATEGORY_TELEPHONE,
                            'selection_country_id' => $employmentData['selection_telephone_country_id'],
                            'contact' => $telephone,
                        ]);
                    }

                    // Create mobile phone contact if available
                    if ($mobilePhone) {
                        $employment->contacts()->create([
                            'category' => Contact::CATEGORY_MOBILE,
                            'selection_country_id' => $employmentData['selection_mobile_country_id'],
                            'contact' => $mobilePhone,
                            'can_receive_sms' => true,
                        ]);
                    }
                }
            } else {
                if ($request->has('company')) {
                    $company = $customer->company()->create([
                        ...$request->company,
                    ]);
                    if ($request->has('owners')) {
                        foreach ($request->owners as $ownerData) {
                            $company->owners()->create([
                                ...$ownerData,
                            ]);
                        }
                    }
                }
            }

            if ($request->has('collateral')) {
                foreach ($request->collateral as $collateralData) {
                    // Extract and remove nested data
                    $propertyData = $collateralData['property'] ?? null;
                    $valuersData = $collateralData['valuers'] ?? [];
                    $ownersData = $collateralData['property_owners'] ?? [];

                    unset($collateralData['property'], $collateralData['property_owners'], $collateralData['valuers']);

                    // Create the Collateral
                    $collateral = Collateral::create([
                        ...$collateralData,
                        'company_id' => $team->company_id,
                        'team_id' => $team->id,
                    ]);

                    $customer->customerCollaterals()->create([
                        'collateral_id' => $collateral->id,
                    ]);

                    // Handle property and nested items
                    if ($propertyData) {
                        $addressData = $propertyData['address'] ?? null;

                        unset($propertyData['address']);

                        $property = $collateral->property()->create($propertyData);

                        if ($addressData) {
                            $property->address()->create([
                                ...$addressData,
                                'is_primary' => true,
                            ]);
                        }
                        if (! empty($ownersData)) {
                            foreach ($ownersData as $ownerData) {
                                $owner = $property->propertyOwners()->create([
                                    'name' => $ownerData['name'],
                                    'identity_no' => $ownerData['identity_no'],
                                    'remark' => $ownerData['remark'] ?? null,
                                ]);

                                // Create owner address
                                if (isset($ownerData['address'])) {
                                    $owner->address()->create([
                                        ...$ownerData['address'],
                                        'is_primary' => true,
                                    ]);
                                }

                                // Create telephone contact
                                if (! empty($ownerData['telephone'])) {
                                    $owner->contacts()->create([
                                        'category' => Contact::CATEGORY_TELEPHONE,
                                        'selection_country_id' => $ownerData['selection_telephone_country_id'] ?? null,
                                        'contact' => $ownerData['telephone'],
                                    ]);
                                }

                                // Create mobile contact
                                if (! empty($ownerData['mobile_phone'])) {
                                    $owner->contacts()->create([
                                        'category' => Contact::CATEGORY_MOBILE,
                                        'selection_country_id' => $ownerData['selection_mobile_country_id'] ?? null,
                                        'contact' => $ownerData['mobile_phone'],
                                        'can_receive_sms' => true,
                                    ]);
                                }
                            }
                        }
                    }

                    // Handle valuers
                    foreach ($valuersData as $valuerData) {
                        $collateral->valuers()->create($valuerData);
                    }
                }
            }
            $teamUuid = Team::where('id', $request->team_id)->value('uuid');

            if ($request->has('document')) {
                foreach ($request->document as $index => $docData) {
                    $uploadedFile = $request->file("document.$index.file");
                    $selection = Selection::find($docData['selection_type_id']);
                    $selectionValue = strtolower(trim($selection?->value)); // e.g., "Customer Document"

                    // Map selection value to folder name
                    $typeMap = [
                        'customer documents' => 'customer-doc',
                        'collateral documents' => 'collateral-doc',
                        'security documents' => 'security-doc',
                    ];
                    $typeFolder = $typeMap[$selectionValue];

                    $customerDocument = $customer->customerDocuments()->create([
                        'selection_type_id' => $docData['selection_type_id'],
                    ]);
                    $filePath = $this->storeUploadedFile($uploadedFile, 'uploads/'.implode('/', [
                        'customer',
                        $customer->uuid,
                        $teamUuid,
                        $typeFolder,
                    ]));

                    $customerDocument->documents()->create([
                        'category' => $docData['type'] === 'application/pdf' ? Document::CATEGORY_PDF : Document::CATEGORY_IMG,
                        'url' => $filePath,
                    ]);
                }
            }

            DB::commit();

            return Redirect::route('customers.index')->with('success', 'Customer created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            return Redirect::back()
                ->with('error', 'Failed to create customer: '.$e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(CustomerProfile $customer): Response
    {
        $this->authorize('view', $customer);

        return Inertia::render('customers/Show', [
            'customer' => $this->transformCustomerDetail($customer),
            'customerTypes' => Selection::where('category', 'customer_type')->get(),
            'collateralTypes' => Selection::where('category', 'collateral_type')->get(),
            'propertyTypes' => Selection::where('category', 'property_type')->get(),
            'landCategories' => Selection::where('category', 'land_category')->get(),
            'landStatuses' => Selection::where('category', 'land_status')->get(),
            'states' => Selection::where('category', 'state')->get(),
            'countries' => Selection::where('category', 'country')->get(),
            'squareTypes' => Selection::where('category', 'square_unit')->get(),
            'telephoneTypes' => Selection::where('category', 'telephone_country')->get(),
            'mobileTypes' => Selection::where('category', 'mobile_country')->get(),
            'contactTypes' => Selection::where('category', 'contact_type')->get(),
            'ownerTypes' => Selection::where('category', 'owner_type')->get(),
            'addressTypes' => Selection::where('category', 'address_type')->get(),
            'businessClassificationTypes' => Selection::where('category', 'business_classification')->get(),
            'termsOfEmploymentTypes' => Selection::where('category', 'terms_of_employment')->get(),
            'occupationsTypes' => Selection::where('category', 'occupation')->get(),
            'nationalities' => Selection::where('category', 'nationality')->get(),
            'genders' => Selection::where('category', 'gender')->get(),
            'educationTypes' => Selection::where('category', 'education_level')->get(),
            'naturesOfBusinessTypes' => Selection::where('category', 'nature_of_business')->get(),
            'maritalStatus' => Selection::where('category', 'marital_status')->get(),
            'races' => Selection::where('category', 'race')->get(),
            'documentTypes' => Selection::where('category', 'document_type')->get(),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CustomerProfile $customer): Response
    {
        $this->authorize('update', $customer);

        return Inertia::render('customers/Edit', [
            'customer' => $this->transformCustomerDetail($customer),
            'customerTypes' => Selection::where('category', 'customer_type')->get(),
            'collateralTypes' => Selection::where('category', 'collateral_type')->get(),
            'propertyTypes' => Selection::where('category', 'property_type')->get(),
            'landCategories' => Selection::where('category', 'land_category')->get(),
            'landStatuses' => Selection::where('category', 'land_status')->get(),
            'states' => Selection::where('category', 'state')->get(),
            'countries' => Selection::where('category', 'country')->get(),
            'squareTypes' => Selection::where('category', 'square_unit')->get(),
            'telephoneCountries' => Selection::where('category', 'telephone_country')->get(),
            'mobileCountries' => Selection::where('category', 'mobile_country')->get(),
            'contactTypes' => Selection::where('category', 'contact_type')->get(),
            'ownerTypes' => Selection::where('category', 'owner_type')->get(),
            'addressTypes' => Selection::where('category', 'address_type')->get(),
            'businessClassificationTypes' => Selection::where('category', 'business_classification')->get(),
            'termsOfEmploymentTypes' => Selection::where('category', 'terms_of_employment')->get(),
            'occupationsTypes' => Selection::where('category', 'occupation')->get(),
            'nationalities' => Selection::where('category', 'nationality')->get(),
            'genders' => Selection::where('category', 'gender')->get(),
            'educationTypes' => Selection::where('category', 'education_level')->get(),
            'naturesOfBusinessTypes' => Selection::where('category', 'nature_of_business')->get(),
            'maritalStatus' => Selection::where('category', 'marital_status')->get(),
            'races' => Selection::where('category', 'race')->get(),
            'documentTypes' => Selection::where('category', 'document_type')->get(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateCustomerRequest $request, CustomerProfile $customer): RedirectResponse
    {
        $this->authorize('update', $customer);

        $customer->load('teams');

        DB::beginTransaction();
        try {
            $customer->update([
                ...$request->except(['team_id', 'contacts', 'addresses', 'employment', 'company', 'owners', 'collateral', 'document']),
            ]);

            if ($request->has('contacts')) {
                foreach ($request->contacts as $contactData) {
                    if (isset($contactData['id']) && isset($contactData['_delete']) && $contactData['_delete']) {
                        $contact = Contact::find($contactData['id']);
                        if ($contact) {
                            $contact->delete();
                        }

                        continue;
                    }

                    if (isset($contactData['id'])) {
                        $contact = Contact::find($contactData['id']);

                        if ($contact) {
                            if (isset($contactData['telephone'])) {
                                $contact->update([
                                    'selection_type_id' => $contactData['selection_type_id'],
                                    'category' => Contact::CATEGORY_TELEPHONE,
                                    'selection_country_id' => $contactData['selection_telephone_country_id'],
                                    'contact' => $contactData['telephone'],
                                    'is_primary' => $contactData['is_primary'],
                                ]);

                            }

                            if (isset($contactData['mobile_phone'])) {
                                $contact->update([
                                    'selection_type_id' => $contactData['selection_type_id'],
                                    'category' => Contact::CATEGORY_MOBILE,
                                    'selection_country_id' => $contactData['selection_mobile_country_id'],
                                    'contact' => $contactData['mobile_phone'],
                                    'can_receive_sms' => $contactData['can_receive_sms'],
                                    'is_primary' => $contactData['is_primary'],
                                ]);
                            }
                        }
                    } else {
                        $customerContact = $customer->customerContacts()->create();
                        if (isset($contactData['telephone']) && $contactData['telephone'] !== null) {
                            $customerContact->contacts()->create([
                                'selection_type_id' => $contactData['selection_type_id'],
                                'category' => Contact::CATEGORY_TELEPHONE,
                                'selection_country_id' => $contactData['selection_telephone_country_id'],
                                'contact' => $contactData['telephone'],
                                'is_primary' => $contactData['is_primary'],
                            ]);
                        }

                        if (isset($contactData['mobile_phone']) && $contactData['mobile_phone'] !== null) {
                            $customerContact->contacts()->create([
                                'selection_type_id' => $contactData['selection_type_id'],
                                'category' => Contact::CATEGORY_MOBILE,
                                'selection_country_id' => $contactData['selection_mobile_country_id'],
                                'contact' => $contactData['mobile_phone'],
                                'can_receive_sms' => $contactData['can_receive_sms'],
                                'is_primary' => $contactData['is_primary'],
                            ]);
                        }
                    }
                }
            }

            if ($request->has('addresses')) {
                foreach ($request->addresses as $addressData) {
                    if (isset($addressData['id']) && isset($addressData['_delete']) && $addressData['_delete']) {
                        $address = Address::find($addressData['id']);
                        if ($address) {
                            $address->delete();
                        }

                        continue;
                    }

                    if (isset($addressData['id'])) {
                        $address = Address::find($addressData['id']);
                        if ($address) {
                            $address->update([
                                ...$addressData,
                            ]);
                        }
                    } else {
                        $customerAddress = $customer->customerAddresses()->create();
                        $address = $customerAddress->addresses()->create([
                            ...$addressData,
                        ]);
                    }
                }
            }

            if ($request->selection_type_id == 28) {
                if ($request->has('employment')) {
                    $employmentData = $request->employment;

                    if (isset($employmentData['id'])) {
                        $employment = CustomerEmployment::find($employmentData['id']);
                        if ($employment) {
                            $employment->update([
                                ...$employmentData,
                            ]);

                            if ($employment->telephone) {
                                $employment->telephone()->update([
                                    'contact' => $employmentData['telephone'],
                                    'selection_telephone_country_id' => $employmentData['selection_telephone_country_id'],
                                ]);
                            } elseif (! $employment->telephone && $employmentData['telephone'] != null) {
                                $employment->contacts()->create([
                                    'category' => Contact::CATEGORY_TELEPHONE,
                                    'selection_country_id' => $employmentData['selection_telephone_country_id'],
                                    'contact' => $employmentData['telephone'],
                                ]);
                            }

                            if ($employment->mobilePhone) {
                                $employment->mobilePhone()->update([
                                    'contact' => $employmentData['mobile_phone'],
                                    'selection_mobile_country_id' => $employmentData['selection_mobile_country_id'],
                                ]);
                            }

                            $employment->address->update([
                                ...$employmentData['address'],
                            ]);
                        }
                    }
                }
            } else {

                if ($request->has('company')) {
                    $company = $customer->company->update([
                        ...$request->company,
                    ]);

                    if ($request->has('owners')) {
                        foreach ($request->owners as $ownerData) {

                            if (isset($ownerData['id']) && isset($ownerData['_delete']) && $ownerData['_delete']) {
                                $owner = Owner::find($ownerData['id']);
                                if ($owner) {
                                    $owner->delete();
                                }

                                continue;
                            }

                            if (isset($ownerData['id'])) {
                                $owner = Owner::find($ownerData['id']);
                                if ($owner) {
                                    $owner->update([
                                        ...$ownerData,
                                    ]);
                                }
                            } else {
                                $customer->company->owners()->create([
                                    ...$ownerData,
                                ]);
                            }
                        }
                    }
                }
            }

            if ($request->has('collateral')) {
                foreach ($request->collateral as $collateralData) {
                    if (isset($collateralData['id']) && isset($collateralData['_delete']) && $collateralData['_delete']) {
                        $collateral = Collateral::find($collateralData['id']);
                        if ($collateral) {
                            $collateral->delete();
                        }

                        continue;
                    }

                    if (isset($collateralData['id'])) {
                        $collateral = Collateral::find($collateralData['id']);
                        if ($collateral) {
                            $collateral->update([
                                ...$collateralData,
                            ]);
                        }

                        $property = CollateralProperty::find($collateralData['property']['id']);
                        if ($property) {
                            $property->update([
                                ...$collateralData['property'],
                            ]);

                        }

                        if ($collateralData['property_owners']) {
                            foreach ($collateralData['property_owners'] as $ownerData) {
                                // Check if owner should be deleted
                                if (isset($ownerData['id']) && isset($ownerData['_delete']) && $ownerData['_delete']) {
                                    $owner = CollateralPropertyOwner::find($ownerData['id']);
                                    if ($owner) {
                                        $owner->delete();
                                    }

                                    continue;
                                }

                                // Update or create owner
                                if (isset($ownerData['id'])) {
                                    $owner = CollateralPropertyOwner::find($ownerData['id']);

                                    if ($owner) {
                                        $owner->update([
                                            'name' => $ownerData['name'],
                                            'identity_no' => $ownerData['identity_no'],
                                            'remark' => $ownerData['remark'] ?? null,
                                        ]);
                                    }
                                } else {
                                    $owner = $property->propertyOwners()->create([
                                        'name' => $ownerData['name'],
                                        'identity_no' => $ownerData['identity_no'],
                                        'remark' => $ownerData['remark'] ?? null,
                                    ]);
                                }

                                // Update or create owner address
                                if (isset($ownerData['address'])) {
                                    $address = $owner->address;
                                    if ($address) {
                                        $address->update([
                                            ...$ownerData['address'],
                                        ]);
                                    } else {
                                        $owner->address()->create([
                                            ...$ownerData['address'],
                                            'is_primary' => true,
                                        ]);
                                    }
                                }

                                // Update or create owner contacts
                                if (isset($ownerData['telephone'])) {
                                    $telephoneContact = $owner->contacts()
                                        ->where('category', Contact::CATEGORY_TELEPHONE)
                                        ->first();

                                    if ($telephoneContact) {
                                        $telephoneContact->update([
                                            'contact' => $ownerData['telephone'],
                                            'selection_country_id' => $ownerData['selection_telephone_country_id'],
                                        ]);
                                    } else {
                                        $owner->contacts()->create([
                                            'category' => Contact::CATEGORY_TELEPHONE,
                                            'contact' => $ownerData['telephone'],
                                            'selection_country_id' => $ownerData['selection_telephone_country_id'],
                                        ]);
                                    }
                                }

                                if (isset($ownerData['mobile_phone'])) {
                                    $mobileContact = $owner->contacts()
                                        ->where('category', Contact::CATEGORY_MOBILE)
                                        ->first();

                                    if ($mobileContact) {
                                        $mobileContact->update([
                                            'contact' => $ownerData['mobile_phone'],
                                            'selection_country_id' => $ownerData['selection_mobile_country_id'],
                                        ]);
                                    } else {
                                        $owner->contacts()->create([
                                            'category' => Contact::CATEGORY_MOBILE,
                                            'contact' => $ownerData['mobile_phone'],
                                            'can_receive_sms' => true,
                                            'selection_country_id' => $ownerData['selection_mobile_country_id'],
                                        ]);
                                    }
                                }
                            }
                        }

                        if ($collateralData['valuers']) {
                            foreach ($collateralData['valuers'] as $valuerData) {
                                // Check if valuer should be deleted
                                if (isset($valuerData['id']) && isset($valuerData['_delete']) && $valuerData['_delete']) {
                                    $valuer = CollateralValuer::find($valuerData['id']);
                                    if ($valuer) {
                                        $valuer->delete();
                                    }

                                    continue;
                                }

                                // Update or create valuer
                                if (isset($valuerData['id'])) {
                                    $valuer = CollateralValuer::find($valuerData['id']);
                                    if ($valuer) {
                                        $valuer->update([
                                            ...$valuerData,
                                        ]);
                                    }
                                } else {
                                    $collateral->valuers()->create([
                                        ...$valuerData,
                                    ]);
                                }
                            }
                        }
                    } else {

                        $propertyData = $collateralData['property'] ?? null;
                        $valuersData = $collateralData['valuers'] ?? [];
                        $ownersData = $collateralData['property_owners'] ?? [];

                        $collateral = Collateral::create([
                            ...$collateralData,
                            'company_id' => $customer->company_id,
                            'team_id' => $customer->teams()->first()->id,
                        ]);

                        $customer->customerCollaterals()->create([
                            'collateral_id' => $collateral->id,
                        ]);

                        if ($propertyData) {
                            $addressData = $propertyData['address'] ?? null;

                            unset($propertyData['address']);

                            $property = $collateral->property()->create($propertyData);

                            if ($addressData) {
                                $property->address()->create([
                                    ...$addressData,
                                    'is_primary' => true,
                                ]);
                            }
                            if (! empty($ownersData)) {
                                foreach ($ownersData as $ownerData) {
                                    $owner = $property->propertyOwners()->create([
                                        'name' => $ownerData['name'],
                                        'identity_no' => $ownerData['identity_no'],
                                        'remark' => $ownerData['remark'] ?? null,
                                    ]);

                                    // Create owner address
                                    if (isset($ownerData['address'])) {
                                        $owner->address()->create([
                                            ...$ownerData['address'],
                                            'is_primary' => true,
                                        ]);
                                    }

                                    // Create telephone contact
                                    if (! empty($ownerData['telephone'])) {
                                        $owner->contacts()->create([
                                            'category' => Contact::CATEGORY_TELEPHONE,
                                            'selection_country_id' => $ownerData['selection_telephone_country_id'] ?? null,
                                            'contact' => $ownerData['telephone'],
                                        ]);
                                    }

                                    // Create mobile contact
                                    if (! empty($ownerData['mobile_phone'])) {
                                        $owner->contacts()->create([
                                            'category' => Contact::CATEGORY_MOBILE,
                                            'selection_country_id' => $ownerData['selection_mobile_country_id'] ?? null,
                                            'contact' => $ownerData['mobile_phone'],
                                            'can_receive_sms' => true,
                                        ]);
                                    }
                                }
                            }
                        }

                        // Handle valuers
                        foreach ($valuersData as $valuerData) {
                            $collateral->valuers()->create($valuerData);
                        }
                    }
                }
            }

            $teamUuid = Team::where('name', $request->team)->value('uuid');

            if ($request->has('docDeleteItems')) {
                foreach ($request->docDeleteItems as $docId => $shouldDelete) {
                    if ($shouldDelete) {
                        // Find the document by ID
                        $document = \App\Models\Document::find($docId);

                        if ($document) {
                            // Delete the file from storage
                            $disk = App::environment('local') ? 'public' : 's3';
                            if (Storage::disk($disk)->exists($document->url)) {
                                Storage::disk($disk)->delete($document->url);
                            }

                            // Delete the database record
                            $document->delete();
                        }
                    }
                }
            }

            // TODO: make it able to upload doc and delete selected doc
            if ($request->has('document')) {
                foreach ($request->document as $index => $docData) {
                    $base64 = $docData['base64'] ?? null;
                    $fileName = $docData['name'] ?? 'unnamed';
                    $selectionTypeId = $docData['selection_type_id'] ?? null;

                    if (! $base64 || ! $selectionTypeId) {
                        continue;
                    }

                    // Decode base64
                    $base64Content = preg_replace('#^data:.*?;base64,#', '', $base64);
                    $decodedFile = base64_decode($base64Content);

                    // Determine save path
                    $selection = Selection::find($selectionTypeId);
                    $selectionValue = strtolower(trim($selection?->value ?? ''));
                    $typeMap = [
                        'customer documents' => 'customer-doc',
                        'collateral documents' => 'collateral-doc',
                        'security documents' => 'security-doc',
                    ];
                    $typeFolder = $typeMap[$selectionValue] ?? 'other-docs';

                    $disk = App::environment('local') ? 'public' : 's3';

                    $filename = pathinfo($fileName, PATHINFO_FILENAME);
                    $extension = pathinfo($fileName, PATHINFO_EXTENSION) ?: 'bin';

                    $directory = 'uploads/customer/'.$customer->uuid.'/'.$teamUuid.'/'.$typeFolder;
                    $finalName = $filename.'.'.$extension;
                    $counter = 1;

                    // Ensure unique file name by checking if it already exists
                    while (Storage::disk($disk)->exists("$directory/$finalName")) {
                        $finalName = "$filename ($counter).$extension";
                        $counter++;
                    }

                    $filePath = "$directory/$finalName";

                    // Save the file to disk
                    Storage::disk($disk)->put($filePath, $decodedFile);

                    // Create related DB entries
                    $customerDocument = $customer->customerDocuments()->create([
                        'selection_type_id' => $selectionTypeId,
                    ]);

                    $customerDocument->documents()->create([
                        'category' => $docData['type'] === 'application/pdf'
                            ? Document::CATEGORY_PDF
                            : Document::CATEGORY_IMG,
                        'url' => $filePath,
                    ]);
                }
            }

            DB::commit();

            return Redirect::route('customers.index')->with('success', 'Customer updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            return Redirect::back()
                ->with('error', 'Failed to update customer: '.$e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CustomerProfile $customerProfile): RedirectResponse
    {
        $this->authorize('delete', $customerProfile);

        return Redirect::route('customers.index')->with('success', 'Customer deleted successfully.');
    }

    public function search(SearchCustomerRequest $request)
    {
        // Retrieve and normalize query parameters
        $identityNo = $request->query('identity_no');
        $teamId = $request->query('team_id');
        $customerType = $request->query('customer_type');
        $name = $request->query('name') ? strtoupper($request->query('name')) : null;

        // Validate that the team exists
        $team = Team::find($teamId);
        if (! $team) {
            return response()->json([
                'message' => 'Team not found.',
            ], 404);
        }

        // Attempt to find the customer with matching details
        $customer = CustomerProfile::with('teams')
            ->where('company_id', $team->company_id)
            ->where('name', $name)
            ->where('identity_no', $identityNo)
            ->first();

        // If customer exists, check if they are part of the specified team
        if ($customer) {
            $isInTeam = $customer->teams->contains('id', $teamId);

            return response()->json([
                'message' => $isInTeam
                    ? 'Customer already exists.'
                    : 'Customer already exists in (company: '.$team->company->display_name.' team: '.$team->name.')',
            ], 400);
        }

        // Customer not found — provide redirect URL to creation form
        return response()->json([
            'exists' => false,
            'redirect' => route('customers.create', [
                'identity_no' => $identityNo,
                'team_id' => $teamId,
                'customer_type' => $customerType,
                'name' => $name,
                'noCustomer' => true,
            ]),
        ]);
    }

    private function transformCustomerDetail(CustomerProfile $customer): array
    {
        $teamName = $customer->teams->first()?->name;

        $contacts = [];

        if ($customer->customerContacts) {
            foreach ($customer->customerContacts as $customerContact) {
                foreach ($customerContact->contacts as $contact) {
                    $contacts[] = [
                        'id' => $contact->id,
                        'uuid' => $contact->uuid,
                        'selection_type_id' => $contact->selection_type_id,
                        'selection_country_id' => $contact->selection_country_id,
                        'contact_country_selection' => $contact->contactCountrySelection ? $contact->contactCountrySelection->value : null,
                        'contact' => $contact->contact,
                        'is_primary' => $contact->is_primary,
                        'can_receive_sms' => $contact->can_receive_sms,
                    ];
                }
            }
        }

        $addresses = [];

        if ($customer->customerAddresses) {
            foreach ($customer->customerAddresses as $customerAddress) {
                foreach ($customerAddress->addresses as $address) {
                    $addresses[] = [
                        'id' => $address->id,
                        'uuid' => $address->uuid,
                        'selection_type_id' => $address->selection_type_id,
                        'line_1' => $address->line_1,
                        'line_2' => $address->line_2,
                        'postcode' => $address->postcode,
                        'city' => $address->city,
                        'selection_state_id' => $address->selection_state_id,
                        'state_selection' => $address->stateSelection ? $address->stateSelection->value : null,
                        'selection_country_id' => $address->selection_country_id,
                        'country_selection' => $address->countrySelection ? $address->countrySelection->value : null,
                        'is_primary' => $address->is_primary,
                    ];
                }
            }
        }

        $companyOwners = [];

        if ($customer->company && $customer->company->owners) {
            foreach ($customer->company->owners as $companyowner) {
                $companyOwners[] = [
                    'id' => $companyowner->id,
                    'uuid' => $companyowner->uuid,
                    'name' => $companyowner->name,
                    'identity_no' => $companyowner->identity_no,
                    'selection_type_id' => $companyowner->selection_type_id,
                    'owner_type' => $companyowner->selectionTypes ? $companyowner->selectionTypes->value : null,
                    'selection_nationality_id' => $companyowner->selection_nationality_id,
                    'nationality_selection' => $companyowner->selectionNationality ? $companyowner->selectionNationality->value : null,
                    'share_unit' => $companyowner->share_unit,
                ];
            }
        }

        $collaterals = [];

        if ($customer->customerCollaterals) {
            foreach ($customer->customerCollaterals as $customerCollateral) {
                $collateral = $customerCollateral->collateral;

                $property = $collateral->property ?? null;

                // Extract valuers
                $valuers = [];
                if ($collateral) {
                    foreach ($collateral->valuers as $valuer) {
                        $valuers[] = [
                            'id' => $valuer->id,
                            'valuer' => $valuer->valuer,
                            'valuation_amount' => $valuer->valuation_amount,
                            'valuation_received_date' => $valuer->valuation_received_date,
                            'land_search_received_date' => $valuer->land_search_received_date,
                            'is_primary' => $valuer->is_primary,
                        ];
                    }

                    $propertyOwner = [];
                    if ($property && $property->propertyOwners) {
                        foreach ($property->propertyOwners as $owner) {
                            $ownerContacts = [];

                            foreach ($owner->contacts as $ownerContact) {
                                $ownerContacts[] = [
                                    'id' => $ownerContact->id ?? null,
                                    'uuid' => $ownerContact->uuid ?? null,
                                    'selection_type_id' => $ownerContact->selection_type_id ?? null,
                                    'selection_country_id' => $ownerContact->selection_country_id ?? null,
                                    'contact' => $ownerContact->contact ?? null,
                                    'is_primary' => $ownerContact->is_primary ?? null,
                                ];
                            }

                            $propertyOwner[] = [
                                'id' => $owner->id,
                                'name' => $owner->name,
                                'identity_no' => $owner->identity_no,
                                'selection_telephone_country_id' => $owner->telephone()?->selection_country_id,
                                'selection_mobile_country_id' => $owner->mobilePhone()?->selection_country_id,
                                'telephone_country_selection' => $owner->telephone()?->contactCountrySelection ? $owner->telephone()->contactCountrySelection->value : null,
                                'mobile_country_selection' => $owner->mobilePhone()?->contactCountrySelection ? $owner->mobilePhone()->contactCountrySelection->value : null,
                                'telephone' => $owner->telephone,
                                'mobile_phone' => $owner->mobilePhone,
                                'address' => [
                                    'id' => $owner->address->id ?? null,
                                    'line_1' => $owner->address->line_1 ?? null,
                                    'line_2' => $owner->address->line_2 ?? null,
                                    'postcode' => $owner->address->postcode ?? null,
                                    'city' => $owner->address->city ?? null,
                                    'selection_state_id' => $owner->address->selection_state_id ?? null,
                                    'selection_country_id' => $owner->address->selection_country_id ?? null,
                                ],
                            ];
                        }
                    }

                    $collaterals[] = [
                        'id' => $collateral->id,
                        'selection_customer_type_id' => $collateral->selection_customer_type_id,
                        'selection_type_id' => $collateral->selection_type_id,
                        'typeSelection' => $collateral->typeSelection ? $collateral->typeSelection->value : null,
                        'name' => $collateral->name,
                        'identity_no' => $collateral->identity_no,
                        'remark' => $collateral->remark,
                        'property' => $property ? [
                            'id' => $property->id,
                            'ownership_no' => $property->ownership_no,
                            'lot_number' => $property->lot_number,
                            'selection_land_category_id' => $property->selection_land_category_id,
                            'land_category' => $property->land_category,
                            'land_category_selection' => $property->landCategorySelection ? $property->landCategorySelection->value : null,
                            'land_category_other' => $property->land_category_other,
                            'type_of_property' => $property->type_of_property,
                            'selection_type_of_property_id' => $property->selection_type_of_property_id,
                            'type_of_property_selection' => $property->propertyTypesSelection ? $property->propertyTypesSelection->value : null,
                            'selection_land_size_unit' => $property->selection_land_size_unit,
                            'land_size_unit_selection' => $property->landSizeSelection ? $property->landSizeSelection->value : null,
                            'land_size' => $property->land_size,
                            'selection_land_status_id' => $property->selection_land_status_id,
                            'land_status' => $property->land_status,
                            'land_status_selection' => $property->landStatusSelection ? $property->landStatusSelection->value : null,
                            'no_syit_piawai' => $property->no_syit_piawai,
                            'certified_plan_no' => $property->certified_plan_no,
                            'selection_built_up_area_unit' => $property->selection_built_up_area_unit,
                            'built_up_area_of_property' => $property->built_up_area_of_property,
                            'built_up_area_unit_selection' => $property->builtUpAreaSelection ? $property->builtUpAreaSelection->value : null,
                            'city' => $property->city,
                            'location' => $property->location,
                            'district' => $property->district,
                            'address' => [
                                'line_1' => $property->address->line_1 ?? null,
                                'line_2' => $property->address->line_2 ?? null,
                                'postcode' => $property->address->postcode ?? null,
                                'city' => $property->address->city ?? null,
                                'selection_state_id' => $property->address->selection_state_id ?? null,
                                'state_selection' => $property->address->stateSelection ? $property->address->stateSelection->value : null,
                                'selection_country_id' => $property->address->selection_country_id ?? null,
                                'country_selection' => $property->address->countrySelection ? $property->address->countrySelection->value : null,
                            ],
                        ] : null,
                        'valuers' => $valuers,
                        'owners' => $propertyOwner,
                    ];

                }
            }
        }

        $documents = [];

        if ($customer->customerDocuments) {
            foreach ($customer->customerDocuments as $customerDocument) {
                foreach ($customerDocument->documents as $document) {

                    $path = $document->url;
                    $fileContent = $this->getUploadedFile($path);

                    $documents[] = [
                        'id' => $document->id,
                        'url' => $path,
                        'category' => $document->category,
                        'selection_type_id' => $customerDocument->selection_type_id,
                        'file' => [
                            'name' => $fileContent['name'] ?? null,
                            'size' => $fileContent['size'] ?? null,
                            'url' => $fileContent['url'] ?? null,
                        ],
                        'created_at' => $document->created_at,
                        'uploaded_by' => $document->createdBy ? [
                            'id' => $document->createdBy->id,
                            'username' => $document->createdBy->username,
                        ] : null,
                    ];
                }
            }
        }

        return [
            'id' => $customer->id,
            'uuid' => $customer->uuid,
            'team' => $teamName,
            'selection_type_id' => $customer->selection_type_id,
            'customer_type' => $customer->selectionType ? $customer->selectionType->value : null,
            'name' => $customer->name,
            'identity_no' => $customer->identity_no,
            'old_identity_no' => $customer->old_identity_no ? $customer->old_identity_no : null,
            'registration_date' => $customer->registration_date ? $customer->registration_date : null,
            'years_of_incorporation' => $customer->years_of_incorporation ? $customer->years_of_incorporation : null,
            'age' => $customer->age ? $customer->age : null,
            'birth_date' => $customer->birth_date ? $customer->birth_date : null,
            'selection_gender_id' => $customer->selection_gender_id ? $customer->selection_gender_id : null,
            'gender_selection' => $customer->selectionGender ? $customer->selectionGender->value : null,
            'selection_race_id' => $customer->selection_race_id ? $customer->selection_race_id : null,
            'race_selection' => $customer->selectionRace ? $customer->selectionRace->value : null,
            'selection_nationality_id' => $customer->selection_nationality_id ? $customer->selection_nationality_id : null,
            'nationality_selection' => $customer->selectionNationality ? $customer->selectionNationality->value : null,
            'selection_education_level_id' => $customer->selection_education_level_id ? $customer->selection_education_level_id : null,
            'education_level_selection' => $customer->selectionEducationLevel ? $customer->selectionEducationLevel->value : null,
            'selection_marriage_status_id' => $customer->selection_marriage_status_id ? $customer->selection_marriage_status_id : null,
            'marriage_status_selection' => $customer->selectionMarriageStatus ? $customer->selectionMarriageStatus->value : null,
            'remark' => $customer->remark ? $customer->remark : null,
            'contacts' => $contacts,
            'addresses' => $addresses,
            'employment' => $customer->employment ? [
                'id' => $customer->employment->id,
                'uuid' => $customer->employment->uuid,
                'employer_name' => $customer->employment->employer_name,
                'length_service_year' => $customer->employment->length_service_year,
                'length_service_month' => $customer->employment->length_service_month,
                'job_position' => $customer->employment->job_position,
                'selection_terms_of_employment_id' => $customer->employment->selection_terms_of_employment_id,
                'terms_of_employment_selection' => $customer->employment->selectionTermsOfEmployment ? $customer->employment->selectionTermsOfEmployment->value : null,
                'selection_occupation_id' => $customer->employment->selection_occupation_id,
                'occupation_selection' => $customer->employment->selectionOccupation ? $customer->employment->selectionOccupation->value : null,
                'selection_business_category_id' => $customer->employment->selection_business_category_id,
                'business_category_selection' => $customer->employment->selectionBusinessCategory ? $customer->employment->selectionBusinessCategory->value : null,
                'gross_income' => $customer->employment->gross_income,
                'net_income' => $customer->employment->net_income,
                'selection_telephone_country_id' => $customer->employment->telephone()?->selection_country_id,
                'selection_mobile_country_id' => $customer->employment->mobilePhone()?->selection_country_id,
                'telephone_country_selection' => $customer->employment->telephone()?->contactCountrySelection ? $customer->employment->telephone()->contactCountrySelection->value : null,
                'mobile_country_selection' => $customer->employment->mobilePhone()?->contactCountrySelection ? $customer->employment->mobilePhone()->contactCountrySelection->value : null,
                'telephone' => $customer->employment->telephone,
                'mobile_phone' => $customer->employment->mobilePhone,
                'address' => $customer->employment->address ? [
                    'id' => $customer->employment->address->id,
                    'line_1' => $customer->employment->address->line_1,
                    'line_2' => $customer->employment->address->line_2,
                    'postcode' => $customer->employment->address->postcode,
                    'city' => $customer->employment->address->city,
                    'selection_state_id' => $customer->employment->address->selection_state_id,
                    'state_selection' => $customer->employment->address->stateSelection ? $customer->employment->address->stateSelection->value : null,
                    'selection_country_id' => $customer->employment->address->selection_country_id,
                    'country_selection' => $customer->employment->address->countrySelection ? $customer->employment->address->countrySelection->value : null,
                ] : null,
            ] : null,
            'company' => $customer->company ? [
                'id' => $customer->company->id,
                'uuid' => $customer->company->uuid,
                'current_paid_up_capital' => $customer->company->current_paid_up_capital,
                'business_turnover' => $customer->company->business_turnover,
                'business_turnover_date' => $customer->company->business_turnover_date,
                'business_net_income' => $customer->company->business_net_income,
                'business_net_income_date' => $customer->company->business_net_income_date,
                'selection_nature_of_business_id' => $customer->company->selection_nature_of_business_id,
                'nature_of_business_selection' => $customer->company->selectionNatureOfBusiness ? $customer->company->selectionNatureOfBusiness->value : null,
                'selection_country_of_business_id' => $customer->company->selection_country_of_business_id,
                'country_of_business_selection' => $customer->company->selectionCountryOfBusiness ? $customer->company->selectionCountryOfBusiness->value : null,
            ] : null,
            'owners' => $companyOwners,
            'collaterals' => $collaterals,
            'documents' => $documents,
        ];
    }
}
