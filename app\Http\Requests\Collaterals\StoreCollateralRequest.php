<?php

namespace App\Http\Requests\Collaterals;

use App\Enums\Collateral\CollateralStatus;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rules\Enum;

class StoreCollateralRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'team_id' => ['required', 'exists:teams,id'],
            'selection_customer_type_id' => ['required', 'exists:selections,id'],
            'selection_type_id' => ['required', 'exists:selections,id'],
            'name' => ['required', 'string', 'max:255'],
            'identity_no' => ['required', 'string', 'max:20'],
            'status' => ['required', new Enum(CollateralStatus::class)],

            // Valuers
            'valuers' => ['required', 'array', 'min:1'],
            'valuers.*.valuation_amount' => ['required', 'numeric', 'min:0'],
            'valuers.*.valuer' => ['required', 'string', 'max:255'],
            'valuers.*.valuation_received_date' => ['required', 'date'],
            'valuers.*.land_search_received_date' => ['nullable', 'date'],
            'valuers.*.is_primary' => ['required', 'boolean'],

            // Property owners
            'property_owners' => ['nullable', 'array'],
            'property_owners.*.name' => ['required', 'string', 'max:255'],
            'property_owners.*.identity_no' => ['required', 'string', 'max:20'],
            'property_owners.*.telephone' => ['nullable', 'string', 'max:20'],
            'property_owners.*.selection_telephone_country_id' => ['nullable', 'exists:selections,id'],
            'property_owners.*.mobile_phone' => ['nullable', 'string', 'max:20'],
            'property_owners.*.selection_mobile_country_id' => ['nullable', 'exists:selections,id'],
            'property_owners.*.remark' => ['nullable', 'string', 'max:1000'],

            // Property owner addresses
            'property_owners.*.address.line_1' => ['nullable', 'string', 'max:255'],
            'property_owners.*.address.line_2' => ['nullable', 'string', 'max:255'],
            'property_owners.*.address.postcode' => ['nullable', 'string', 'max:10'],
            'property_owners.*.address.city' => ['nullable', 'string', 'max:50'],
            'property_owners.*.address.selection_state_id' => ['nullable', 'exists:selections,id'],
            'property_owners.*.address.state' => ['nullable', 'string', 'max:50'],
            'property_owners.*.address.selection_country_id' => ['nullable', 'exists:selections,id'],
            'property_owners.*.address.country' => ['nullable', 'string', 'max:50'],
        ];

        if ((int) $this->input('selection_customer_type_id') === 29) {
            $rules += [
                'company_name' => ['required', 'string'],
                'business_registration_no' => ['required', 'string', 'max:100'],
            ];
        }

        // Only include property.* rules if selection_type_id == 14
        if ((int) $this->input('selection_type_id') === 14) {
            $rules += [
                'property.ownership_no' => ['required', 'string', 'max:50'],
                'property.lot_number' => ['required', 'string', 'max:50'],
                'property.selection_land_category_id' => ['required', 'exists:selections,id'],
                'property.land_category' => ['nullable', 'string', 'max:50'],
                'property.land_category_other' => ['nullable', 'string', 'max:100'],
                'property.selection_type_of_property_id' => ['required', 'exists:selections,id'],
                'property.type_of_property' => ['nullable', 'string', 'max:50'],
                'property.land_size' => ['required', 'string', 'max:50'],
                'property.selection_land_size_unit' => ['required', 'exists:selections,id'],
                'property.land_size_unit' => ['nullable', 'string', 'max:50'],
                'property.selection_land_status_id' => ['required', 'exists:selections,id'],
                'property.land_status' => ['nullable', 'string', 'max:50'],
                'property.city' => ['required', 'string', 'max:50'],
                'property.location' => ['required', 'string'],
                'property.district' => ['required', 'string'],
                'property.no_syit_piawai' => ['required', 'string', 'max:50'],
                'property.certified_plan_no' => ['nullable', 'string', 'max:50'],
                'property.built_up_area_of_property' => ['nullable', 'string', 'max:50'],
                'property.selection_built_up_area_unit' => ['nullable', 'exists:selections,id'],
                'property.built_up_area_unit' => ['nullable', 'string', 'max:50'],

                // Address
                'property.address.line_1' => ['required', 'string', 'max:255'],
                'property.address.line_2' => ['nullable', 'string', 'max:255'],
                'property.address.postcode' => ['required', 'string', 'max:10'],
                'property.address.city' => ['required', 'string', 'max:50'],
                'property.address.selection_state_id' => ['required', 'exists:selections,id'],
                'property.address.state' => ['nullable', 'string', 'max:50'],
                'property.address.selection_country_id' => ['required', 'exists:selections,id'],
                'property.address.country' => ['nullable', 'string', 'max:50'],
            ];
        } else {
            $rules += [
                'remark' => ['required', 'string', 'max:1000'],
            ];
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            'identity_no' => 'The owner IC is required.',
            'team_id.required' => 'The team is required.',
            'team_id.exists' => 'The selected team is invalid.',
            'selection_customer_type_id.required' => 'The customer type selection is required.',
            'selection_customer_type_id.exists' => 'The selected customer type is invalid.',
            'selection_type_id.required' => 'The collateral type selection is required.',
            'selection_type_id.exists' => 'The selected collateral type is invalid.',
            'name.required' => 'The name is required.',
            'name.string' => 'The name must be a string.',
            'name.max' => 'The name may not be greater than 255 characters.',
            'status.required' => 'The status is required.',
            'status.enum' => 'The selected status is invalid.',
            'valuers.required' => 'At least one valuer is required.',
            'valuers.array' => 'The valuers must be an array.',
            'valuers.min' => 'At least one valuer is required.',
            'valuers.*.valuation_amount.required' => 'The valuation amount is required.',
            'valuers.*.valuation_amount.numeric' => 'The valuation amount must be a numberic.',
            'valuers.*.valuation_amount.min' => 'The valuation amount must be at least 0.',
            'valuers.*.valuer.required' => 'The valuer is required.',
            'valuers.*.valuer.string' => 'The valuer must be a string.',
            'valuers.*.valuer.max' => 'The valuer may not be greater than 255 characters.',
            'valuers.*.is_primary.required' => 'The primary valuer selection is required.',
            'valuers.*.is_primary.boolean' => 'The primary valuer selection must be true or false.',
            'valuers.*.valuation_received_date.required' => 'The valuation received date is required.',
            'valuers.*.valuation_received_date.date' => 'The valuation received date must be a valid date.',
            'valuers.*.land_search_received_date.date' => 'The land search received date must be a valid date.',
            'property_owners.*.address.line_1.required' => 'The address line 1 is required.',
            'property_owners.*.address.postcode.required' => 'The address postcode is required.',
            'property_owners.*.address.city.required' => 'The address city is required.',
            'property_owners.*.address.selection_state_id.required' => 'The address state selection is required.',
            'property_owners.*.address.selection_country_id.required' => 'The address country selection is required.',
            'property_owners.*.address.line_1.string' => 'The address line 1 must be a string.',
            'property_owners.*.address.line_2.string' => 'The address line 2 must be a string.',
            'property_owners.*.address.postcode.string' => 'The address postcode must be a string.',
            'property_owners.*.address.city.string' => 'The address city must be a string.',
            'property_owners.*.address.state.string' => 'The address state must be a string.',
            'property_owners.*.address.country.string' => 'The address country must be a string.',
            'property_owners.*.address.line_1.max' => 'The address line 1 may not be greater than 255 characters.',
            'property_owners.*.address.line_2.max' => 'The address line 2 may not be greater than 255 characters.',
            'property_owners.*.address.postcode.max' => 'The address postcode may not be greater than 10 characters.',
            'property_owners.*.address.city.max' => 'The address city may not be greater than 50 characters.',
            'property_owners.*.address.state.max' => 'The address state may not be greater than 50 characters.',
            'property_owners.*.address.country.max' => 'The address country may not be greater than 50 characters.',
            'property_owners.*.name.required' => 'The name is required.',
            'property_owners.*.name.string' => 'The name must be a string.',
            'property_owners.*.name.max' => 'The name may not be greater than 255 characters.',
            'property_owners.*.identity_no.required' => 'The I/C no is required.',
            'property_owners.*.identity_no.string' => 'The I/C no must be a string.',
            'property_owners.*.identity_no.max' => 'The I/C no may not be greater than 20 characters.',
            'property_owners.*.telephone.string' => 'The telephone must be a string.',
            'property_owners.*.telephone.max' => 'The telephone may not be greater than 20 characters.',
            'property_owners.*.selection_telephone_country_id.exists' => 'The selected property owner telephone country is invalid.',
            'property_owners.*.mobile_phone.string' => 'The mobile phone must be a string.',
            'property_owners.*.mobile_phone.max' => 'The mobile phone may not be greater than 20 characters.',
            'property_owners.*.selection_mobile_country_id.exists' => 'The selected property owner mobile country is invalid.',
            'property_owners.*.remark.string' => 'The remark must be a string.',
            'property_owners.*.remark.max' => 'The remark may not be greater than 1000 characters.',
            'company_name.required' => 'The company name is required.',
            'company_name.string' => 'The company name must be a string.',
            'business_registration_no.required' => 'The business registration number is required.',
            'business_registration_no.string' => 'The business registration number must be a string.',
            'business_registration_no.max' => 'The business registration number may not be greater than 100 characters.',
            'property.ownership_no.required' => 'The ownership number is required.',
            'property.ownership_no.string' => 'The ownership number must be a string.',
            'property.ownership_no.max' => 'The ownership number may not be greater than 50 characters.',
            'property.lot_number.required' => 'The lot number is required.',
            'property.lot_number.string' => 'The lot number must be a string.',
            'property.lot_number.max' => 'The lot number may not be greater than 50 characters.',
            'property.selection_land_category_id.required' => 'The land category selection is required.',
            'property.selection_land_category_id.exists' => 'The selected land category is invalid.',
            'property.land_category.string' => 'The land category must be a string.',
            'property.land_category.max' => 'The land category may not be greater than 50 characters.',
            'property.land_category_other.string' => 'The land category other must be a string.',
            'property.land_category_other.max' => 'The land category other may not be greater than 100 characters.',
            'property.selection_type_of_property_id.required' => 'The type of property selection is required.',
            'property.selection_type_of_property_id.exists' => 'The selected type of property is invalid.',
            'property.type_of_property.string' => 'The type of property must be a string.',
            'property.type_of_property.max' => 'The type of property may not be greater than 50 characters.',
            'property.land_size.required' => 'The land size is required.',
            'property.land_size.string' => 'The land size must be a string.',
            'property.land_size.max' => 'The land size may not be greater than 50 characters.',
            'property.selection_land_size_unit.required' => 'The land size unit selection is required.',
            'property.selection_land_size_unit.exists' => 'The selected land size unit is invalid.',
            'property.land_size_unit.string' => 'The land size unit must be a string.',
            'property.land_size_unit.max' => 'The land size unit may not be greater than 50 characters.',
            'property.selection_land_status_id.required' => 'The land status selection is required.',
            'property.selection_land_status_id.exists' => 'The selected land status is invalid.',
            'property.land_status.string' => 'The land status must be a string.',
            'property.land_status.max' => 'The land status may not be greater than 50 characters.',
            'property.city.required' => 'The city is required.',
            'property.city.string' => 'The city must be a string.',
            'property.city.max' => 'The city may not be greater than 50 characters.',
            'property.location.required' => 'The location is required.',
            'property.location.string' => 'The location must be a string.',
            'property.district.required' => 'The district is required.',
            'property.district.string' => 'The district must be a string.',
            'property.no_syit_piawai.required' => 'The no syit piawai is required.',
            'property.no_syit_piawai.string' => 'The no syit piawai must be a string.',
            'property.no_syit_piawai.max' => 'The no syit piawai may not be greater than 50 characters.',
            'property.certified_plan_no.string' => 'The certified plan number must be a string.',
            'property.certified_plan_no.max' => 'The certified plan number may not be greater than 50 characters.',
            'property.built_up_area_of_property.string' => 'The built-up area of property must be a string.',
            'property.built_up_area_of_property.max' => 'The built-up area of property may not be greater than 50 characters.',
            'property.selection_built_up_area_unit.exists' => 'The selected built-up area unit is invalid.',
            'property.built_up_area_unit.string' => 'The built-up area unit must be a string.',
            'property.built_up_area_unit.max' => 'The built-up area unit may not be greater than 50 characters.',
            'property.address.line_1.required' => 'The address line 1 is required.',
            'property.address.line_1.string' => 'The address line 1 must be a string.',
            'property.address.line_1.max' => 'The address line 1 may not be greater than 255 characters.',
            'property.address.line_2.string' => 'The address line 2 must be a string.',
            'property.address.line_2.max' => 'The address line 2 may not be greater than 255 characters.',
            'property.address.postcode.required' => 'The postcode is required.',
            'property.address.postcode.string' => 'The postcode must be a string.',
            'property.address.postcode.max' => 'The postcode may not be greater than 10 characters.',
            'property.address.city.required' => 'The city is required.',
            'property.address.city.string' => 'The city must be a string.',
            'property.address.city.max' => 'The city may not be greater than 50 characters.',
            'property.address.selection_state_id.required' => 'The state selection is required.',
            'property.address.selection_state_id.exists' => 'The selected address state is invalid.',
            'property.address.state.string' => 'The state must be a string.',
            'property.address.state.max' => 'The state may not be greater than 50 characters.',
            'property.address.selection_country_id.required' => 'The country selection is required.',
            'property.address.selection_country_id.exists' => 'The selected address country is invalid.',
            'property.address.country.string' => 'The country must be a string.',
            'property.address.country.max' => 'The country may not be greater than 50 characters.',
        ];
    }
}
