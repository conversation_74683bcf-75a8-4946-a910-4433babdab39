<?php

namespace App\Traits;

use App\Enums\Loan\LoanInstallmentStatus;
use App\Models\LoanInstallment;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

trait HasLoanInstallments
{
    use GeneratesLoanTransactions;

    /**
     * Generate loan installments.
     * This method calculates the installments based on the loan's principal, interest rate, tenure, and other parameters.
     * It creates installment records in the database.
     */
    public function generateInstallments(): void
    {
        try {
            $principal = $this->loan_principle_amount;
            $rate = $this->interest;
            $tenure = $this->no_of_instalment;
            $rebate = $this->rebate ?? 0.0;
            $lastPayment = $this->last_payment ?? 0.0;
            $isMonthly = $this->selection_mode_type_id === 384;
            $startDate = $this->validateCommencementDate();

            [$interestPerInstallNet, $interestPerInstallGross, $regularPrincipal, $lastPrincipal, $hasLastPayment] =
                $this->calculateInstallmentParts($principal, $rate, $tenure, $rebate, $lastPayment);

            $installments = [];

            for ($i = 1; $i <= $tenure; $i++) {
                $payDate = $isMonthly
                    ? $startDate->copy()->addMonths($i - 1)
                    : $startDate->copy()->addYears($i - 1);

                $dueDate = $isMonthly
                    ? $payDate->copy()->addMonth()->subDay()
                    : $payDate->copy()->addYear()->subDay();

                $isLast = $i === $tenure;
                $principalAmount = $hasLastPayment
                    ? ($isLast ? $lastPrincipal : $regularPrincipal)
                    : $regularPrincipal;

                $totalAmount = round($principalAmount + $interestPerInstallNet, 2);

                $installments[] = [
                    'uuid' => Str::uuid(),
                    'code' => Str::uuid(),
                    'loan_id' => $this->id,
                    'pay_date' => $payDate->format('Y-m-d'),
                    'due_date' => $dueDate->format('Y-m-d'),
                    'tenure' => $i,
                    'total_amount' => $totalAmount,
                    'principle_amount' => $principalAmount,
                    'interest_amount_net' => $interestPerInstallNet,
                    'interest_amount_gross' => $interestPerInstallGross,
                    'status' => LoanInstallmentStatus::UNPAID->value,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            $outstandingBalance = array_sum(array_column($installments, 'total_amount'));
            foreach ($installments as &$installment) {
                $outstandingBalance -= $installment['total_amount'];
                $installment['outstanding_balance_amount'] = round($outstandingBalance, 2);
            }

            DB::transaction(function () use ($installments) {
                LoanInstallment::insert($installments);

                $createdInstallments = LoanInstallment::where('loan_id', $this->id)
                    ->orderBy('tenure')
                    ->get()
                    ->toArray();

                $this->generateInstallmentTransactions($createdInstallments, $this->id);
            });
        } catch (\Throwable $e) {
            Log::error('Installment generation failed', [
                'loan_id' => $this->id,
                'error' => $e->getMessage(),
            ]);

            throw new \RuntimeException('Failed to generate loan installments.', 0, $e);
        }
    }

    /**
     * Validate and parse the commencement date.
     *
     * @throws \InvalidArgumentException
     */
    protected function validateCommencementDate(): Carbon
    {
        if (empty($this->commencement_date)) {
            throw new \InvalidArgumentException('Commencement date is required.');
        }

        try {
            return Carbon::parse($this->commencement_date);
        } catch (\Exception $e) {
            throw new \InvalidArgumentException("Invalid commencement date format: {$this->commencement_date}");
        }
    }

    /**
     * Calculate per-installment amounts.
     */
    protected function calculateInstallmentParts(float $principal, float $rate, int $tenure, float $rebate, float $lastPayment): array
    {
        $totalInterestGross = ($principal * $rate / 100) * $tenure;
        $totalInterestNet = $totalInterestGross - $rebate;
        $interestPerInstallGross = round($totalInterestGross / $tenure, 2);
        $interestPerInstallNet = round($totalInterestNet / $tenure, 2);
        $hasLastPayment = $lastPayment > 0;
        $lastPrincipal = $hasLastPayment ? max(0, round($lastPayment - $interestPerInstallNet, 2)) : 0.0;
        $remainingPrincipal = $principal - $lastPrincipal;
        $regularPrincipal = round($remainingPrincipal / ($hasLastPayment ? $tenure - 1 : $tenure), 2);

        return [$interestPerInstallNet, $interestPerInstallGross, $regularPrincipal, $lastPrincipal, $hasLastPayment];
    }
}
